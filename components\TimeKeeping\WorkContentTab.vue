<template>
  <div class="h-full flex flex-col p-4 space-y-4">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <h3 class="text-lg font-semibold text-gray-900">Chi tiết công việc</h3>

        <!-- Work Effort ID Badge -->
        <div
          v-if="selectedWorkEffortId"
          class="px-2 py-0.5 bg-primary/10 text-primary text-xs font-medium rounded-full"
        >
          ID: {{ selectedWorkEffortId?.id }}
        </div>
      </div>

      <!-- Loading Indicator -->
      <div
        v-if="isLoading"
        class="flex items-center gap-2 text-sm text-gray-500"
      >
        <div
          class="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"
        ></div>
        <span>Đang tải...</span>
      </div>
    </div>

    <!-- No Selection State -->
    <div
      v-if="!selectedWorkEffortId"
      class="flex flex-col items-center justify-center py-12 text-center"
    >
      <div
        class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-8 h-8 text-gray-400"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 00.75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 00-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0112 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 01-.673-.38m0 0A2.18 2.18 0 013 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 013.413-.387m7.5 0V5.25A2.25 2.25 0 0013.5 3h-3a2.25 2.25 0 00-2.25 2.25v.894m7.5 0a48.667 48.667 0 00-7.5 0M12 12.75h.008v.008H12v-.008z"
          />
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-700 mb-1">
        Chưa chọn công việc
      </h3>
      <p class="text-sm text-gray-500 max-w-md">
        Vui lòng chọn một công việc từ danh sách bên trái để xem chi tiết
      </p>
    </div>

    <!-- Editor -->
    <Editor :data="contentData?.content" />
    <!-- Debug Info (only in development) -->
    <div v-if="false" class="mt-4 p-3 bg-gray-100 rounded-lg text-xs font-mono">
      <pre>{{
        JSON.stringify(
          { selectedWorkEffortId, contentData, isLoading },
          null,
          2
        )
      }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";

// Props
const props = defineProps<{
  selectedWorkEffortId: any | null;
}>();

// Composables
const { getContent, handleSaveTextEditor } = usePortal();

// Reactive state
const isLoading = ref(false);
const contentData = ref<any>(null);

// Methods
const handleUpdateContent = async (content: string) => {
  if (!props.selectedWorkEffortId?.id) return;

  const data = {
    type: "DESCRIPTION_WORKEFFORT",
    content: content,
    relativeId: props.selectedWorkEffortId?.id,
    version: new Date(),
    createdBy: "hung",
  };

  // try {
  //   await handleSaveTextEditor(data);
  // } catch (error) {
  //   throw error;
  // }
};

const handleGetContent = async () => {
  if (!props.selectedWorkEffortId?.id) {
    return;
  }

  try {
    isLoading.value = true;
    const response = await getContent(
      "DESCRIPTION_WORKEFFORT",
      props.selectedWorkEffortId?.id
    );

    contentData.value = response;
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};

// Watch for selectedWorkEffortId changes
watch(
  () => props.selectedWorkEffortId?.id,
  (newWorkEffortId, oldWorkEffortId) => {
    if (newWorkEffortId) {
      handleGetContent();
    } else {
      contentData.value = null;
    }
  },
  { immediate: true }
);
</script>
