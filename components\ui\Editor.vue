<template>
  <div class="editor-container">
    <ckeditor
      v-model="dataV2"
      :editor="ClassicEditor"
      :config="config"
      @ready="onReady"
    />
  </div>
</template>

<script setup>
import { computed } from "vue";
import {
  ClassicEditor,
  Essentials,
  Paragraph,
  Bold,
  Italic,
  TodoList,
} from "ckeditor5";
import { Ckeditor } from "@ckeditor/ckeditor5-vue";

import "ckeditor5/ckeditor5.css";

const props = defineProps({
  data: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:data", "save"]);

const dataV2 = computed({
  get: () => props.data,
  set: (value) => emit("update:data", value),
});

let editorInstance = null;

const onReady = (editor) => {
  editorInstance = editor;

  // Thêm nút lưu tùy chỉnh
  editor.ui.componentFactory.add("saveButton", (locale) => {
    const view = new editor.ui.ButtonView(locale);

    view.set({
      label: "Lưu",
      icon: `<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path d="M17 4v12a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h8l4 4z"/>
        <path d="M13 2v6h4"/>
        <path d="M7 10h6"/>
        <path d="M7 14h6"/>
      </svg>`,
      tooltip: "Lưu nội dung",
      class: "ck-button-save",
    });

    view.on("execute", () => {
      emit("save", editor.getData());
    });

    return view;
  });
};

const config = computed(() => {
  return {
    licenseKey: "GPL",
    plugins: [Essentials, Paragraph, Bold, Italic, TodoList],
    toolbar: ["bold", "italic", "|", "todoList", "|", "saveButton"],
  };
});
</script>

<style scoped>
.editor-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
}

/* Thêm padding cho nội dung editor */
:deep(.ck-editor__editable) {
  padding: 1rem !important;
}

:deep(.ck-button-save) {
  background-color: #3b82f6 !important;
  color: white !important;
}

:deep(.ck-button-save:hover) {
  background-color: #2563eb !important;
}

:deep(.ck-button-save svg) {
  fill: white !important;
}
</style>
