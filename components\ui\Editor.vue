<template>
  <div class="editor-container">
    <ckeditor
      v-model="dataV2"
      :editor="ClassicEditor"
      :config="config"
      @ready="onReady"
    />
  </div>
</template>

<script setup>
import { computed } from "vue";
import {
  ClassicEditor,
  Essentials,
  Paragraph,
  Bold,
  Italic,
  TodoList,
  Heading,
  List,
  BlockQuote,
  Undo,
  Autosave,
} from "ckeditor5";
import { Ckeditor } from "@ckeditor/ckeditor5-vue";

import "ckeditor5/ckeditor5.css";

const props = defineProps({
  data: {
    type: String,
    default: "",
  },
  placeholder: {
    type: String,
    default: "Nhập nội dung...",
  },
  showCharacterCount: {
    type: Boolean,
    default: false,
  },
  editable: {
    type: Boolean,
    default: true,
  },
  minHeight: {
    type: String,
    default: "200px",
  },
  autosave: {
    type: Boolean,
    default: true,
  },
  autosaveDelay: {
    type: Number,
    default: 2000, // 2 seconds
  },
});

const emit = defineEmits(["update:data", "save", "autosave"]);

const dataV2 = computed({
  get: () => props.data,
  set: (value) => emit("update:data", value),
});

let editorInstance = null;

const onReady = (editor) => {
  editorInstance = editor;

  // Thêm nút lưu tùy chỉnh
  editor.ui.componentFactory.add("saveButton", (locale) => {
    const view = new editor.ui.ButtonView(locale);

    view.set({
      label: "Lưu",
      icon: `<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path d="M17 4v12a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h8l4 4z"/>
        <path d="M13 2v6h4"/>
        <path d="M7 10h6"/>
        <path d="M7 14h6"/>
      </svg>`,
      tooltip: "Lưu nội dung",
      class: "ck-button-save",
    });

    view.on("execute", () => {
      emit("save", editor.getData());
    });

    return view;
  });
};

const config = computed(() => {
  const basePlugins = [
    Essentials,
    Paragraph,
    Bold,
    Italic,
    TodoList,
    Heading,
    List,
    BlockQuote,
    Undo,
  ];

  // Thêm Autosave plugin nếu được bật
  if (props.autosave) {
    basePlugins.push(Autosave);
  }

  return {
    licenseKey: "GPL",
    plugins: basePlugins,
    toolbar: [
      "undo",
      "redo",
      "|",
      "bold",
      "italic",
      "|",
      "heading",
      "|",
      "bulletedList",
      "numberedList",
      "|",
      "todoList",
      "|",
      "blockQuote",
      "|",
      "saveButton",
    ],
    placeholder: props.placeholder,
    // Cấu hình autosave
    ...(props.autosave && {
      autosave: {
        save: (editor) => {
          const data = editor.getData();
          emit("autosave", data);
          return Promise.resolve();
        },
        waitingTime: props.autosaveDelay,
      },
    }),
  };
});
</script>

<style scoped>
.editor-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
}

/* Styling cho nội dung editor */
:deep(.ck-editor__editable) {
  padding-left: 2rem !important;
  min-height: v-bind(minHeight) !important;
  outline: none !important;
}

/* Placeholder styling */
:deep(.ck-editor__editable.ck-placeholder::before) {
  color: #9ca3af !important;
}

/* Heading styles */
:deep(.ck-editor__editable h1) {
  font-size: 2em !important;
  font-weight: bold !important;
  margin: 0.67em 0 !important;
}

:deep(.ck-editor__editable h2) {
  font-size: 1.5em !important;
  font-weight: bold !important;
  margin: 0.75em 0 !important;
}

/* List styles */
:deep(.ck-editor__editable ul, .ck-editor__editable ol) {
  padding-left: 1.5em !important;
}

/* Blockquote styles */
:deep(.ck-editor__editable blockquote) {
  border-left: 4px solid #e5e7eb !important;
  padding-left: 1em !important;
  margin: 1em 0 !important;
  font-style: italic !important;
}

/* Todo list styles */
:deep(.ck-editor__editable .todo-list) {
  list-style: none !important;
  padding-left: 0 !important;
}

:deep(.ck-editor__editable .todo-list li) {
  display: flex !important;
  align-items: flex-start !important;
  margin: 0.5em 0 !important;
}

:deep(.ck-editor__editable .todo-list li .todo-list__label) {
  flex: 0 0 auto !important;
  margin-right: 0.5rem !important;
  user-select: none !important;
}

:deep(
    .ck-editor__editable .todo-list li .todo-list__label input[type="checkbox"]
  ) {
  cursor: pointer !important;
}

:deep(.ck-button-save) {
  background-color: #3b82f6 !important;
  color: white !important;
}

:deep(.ck-button-save:hover) {
  background-color: #2563eb !important;
}

:deep(.ck-button-save svg) {
  fill: white !important;
}
</style>
